<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="mathilavdell" failures="0" tests="11" name="UserServiceTests" time="0.466" errors="0" timestamp="2025-05-28T00:26:53 IST">
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUser" time="0.011"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUserWithExistingEmail" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUser" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUserNotFound" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetAllUsers" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByEmail" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserById" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByIdNotFound" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserCount" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUser" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUserNotFound" time="0.0"/>
</testsuite> <!-- UserServiceTests -->
