<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "https://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
<title>TestNG Report</title>
<style type="text/css">table {margin-bottom:10px;border-collapse:collapse;empty-cells:show}th,td {border:1px solid #009;padding:.25em .5em}th {vertical-align:bottom}td {vertical-align:top}table a {font-weight:bold}.stripe td {background-color: #E6EBF9}.num {text-align:right}.passedodd td {background-color: #3F3}.passedeven td {background-color: #0A0}.skippedodd td {background-color: #DDD}.skippedeven td {background-color: #CCC}.failedodd td,.attn {background-color: #F33}.failedeven td,.stripe .attn {background-color: #D00}.stacktrace {white-space:pre;font-family:monospace}.totop {font-size:85%;text-align:center;border-bottom:2px solid #000}.invisible {display:none}</style>
</head>
<body>
<table>
<tr><th>Test</th><th># Passed</th><th># Skipped</th><th># Retried</th><th># Failed</th><th>Time (ms)</th><th>Included Groups</th><th>Excluded Groups</th></tr>
<tr><th colspan="8">SpringBootTestNGSuite</th></tr>
<tr><td><a href="#t0">UserServiceTests</a></td><td class="num">11</td><td class="num">0</td><td class="num">0</td><td class="num">0</td><td class="num">421</td><td></td><td></td></tr>
<tr class="stripe"><td><a href="#t1">UserControllerTests</a></td><td class="num">9</td><td class="num">0</td><td class="num">0</td><td class="num">0</td><td class="num">1,099</td><td></td><td></td></tr>
<tr><td><a href="#t2">UserModelTests</a></td><td class="num">6</td><td class="num">0</td><td class="num">0</td><td class="num">0</td><td class="num">68</td><td></td><td></td></tr>
<tr><th>Total</th><th class="num">26</th><th class="num">0</th><th class="num">0</th><th class="num">0</th><th class="num">1,588</th><th colspan="2"></th></tr>
</table>
<table id='summary'><thead><tr><th>Class</th><th>Method</th><th>Start</th><th>Time (ms)</th></tr></thead><tbody><tr><th colspan="4">SpringBootTestNGSuite</th></tr></tbody><tbody id="t0"><tr><th colspan="4">UserServiceTests &#8212; passed</th></tr><tr class="passedeven"><td rowspan="11">com.example.demo.service.UserServiceTest</td><td><a href="#m0">testCreateUser</a></td><td rowspan="1">1748372581622</td><td rowspan="1">11</td></tr><tr class="passedeven"><td><a href="#m1">testCreateUserWithExistingEmail</a></td><td rowspan="1">1748372581635</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m2">testDeleteUser</a></td><td rowspan="1">1748372581636</td><td rowspan="1">1</td></tr><tr class="passedeven"><td><a href="#m3">testDeleteUserNotFound</a></td><td rowspan="1">1748372581638</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m4">testGetAllUsers</a></td><td rowspan="1">1748372581639</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m5">testGetUserByEmail</a></td><td rowspan="1">1748372581640</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m6">testGetUserById</a></td><td rowspan="1">1748372581641</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m7">testGetUserByIdNotFound</a></td><td rowspan="1">1748372581642</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m8">testGetUserCount</a></td><td rowspan="1">1748372581643</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m9">testUpdateUser</a></td><td rowspan="1">1748372581645</td><td rowspan="1">1</td></tr><tr class="passedeven"><td><a href="#m10">testUpdateUserNotFound</a></td><td rowspan="1">1748372581647</td><td rowspan="1">0</td></tr></tbody>
<tbody id="t1"><tr><th colspan="4">UserControllerTests &#8212; passed</th></tr><tr class="passedeven"><td rowspan="9">com.example.demo.controller.UserControllerTest</td><td><a href="#m11">testCreateUser</a></td><td rowspan="1">1748372582200</td><td rowspan="1">273</td></tr><tr class="passedeven"><td><a href="#m12">testCreateUserWithInvalidData</a></td><td rowspan="1">1748372582497</td><td rowspan="1">49</td></tr><tr class="passedeven"><td><a href="#m13">testDeleteUser</a></td><td rowspan="1">1748372582562</td><td rowspan="1">8</td></tr><tr class="passedeven"><td><a href="#m14">testGetAllUsers</a></td><td rowspan="1">1748372582594</td><td rowspan="1">13</td></tr><tr class="passedeven"><td><a href="#m15">testGetUserByEmail</a></td><td rowspan="1">1748372582635</td><td rowspan="1">30</td></tr><tr class="passedeven"><td><a href="#m16">testGetUserById</a></td><td rowspan="1">1748372582677</td><td rowspan="1">6</td></tr><tr class="passedeven"><td><a href="#m17">testGetUserByIdNotFound</a></td><td rowspan="1">1748372582701</td><td rowspan="1">4</td></tr><tr class="passedeven"><td><a href="#m18">testGetUserCount</a></td><td rowspan="1">1748372582724</td><td rowspan="1">7</td></tr><tr class="passedeven"><td><a href="#m19">testUpdateUser</a></td><td rowspan="1">1748372582746</td><td rowspan="1">21</td></tr></tbody>
<tbody id="t2"><tr><th colspan="4">UserModelTests &#8212; passed</th></tr><tr class="passedeven"><td rowspan="6">com.example.demo.model.UserTest</td><td><a href="#m20">testUserConstructor</a></td><td rowspan="1">1748372582776</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m21">testUserToString</a></td><td rowspan="1">1748372582782</td><td rowspan="1">0</td></tr><tr class="passedeven"><td><a href="#m22">testUserWithBlankName</a></td><td rowspan="1">1748372582789</td><td rowspan="1">11</td></tr><tr class="passedeven"><td><a href="#m23">testUserWithInvalidEmail</a></td><td rowspan="1">1748372582807</td><td rowspan="1">11</td></tr><tr class="passedeven"><td><a href="#m24">testUserWithShortPhone</a></td><td rowspan="1">1748372582825</td><td rowspan="1">4</td></tr><tr class="passedeven"><td><a href="#m25">testValidUser</a></td><td rowspan="1">1748372582833</td><td rowspan="1">4</td></tr></tbody>
</table>
<h2>UserServiceTests</h2><h3 id="m0">com.example.demo.service.UserServiceTest#testCreateUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m1">com.example.demo.service.UserServiceTest#testCreateUserWithExistingEmail</h3><table class="result"><tr><th>Expected Exception</th></tr><tr><td><div class="stacktrace">java.lang.RuntimeException: User <NAME_EMAIL> already exists
	at com.example.demo.service.UserService.createUser(UserService.java:31)
	at com.example.demo.service.UserServiceTest.testCreateUserWithExistingEmail(UserServiceTest.java:115)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 26 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m2">com.example.demo.service.UserServiceTest#testDeleteUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m3">com.example.demo.service.UserServiceTest#testDeleteUserNotFound</h3><table class="result"><tr><th>Expected Exception</th></tr><tr><td><div class="stacktrace">java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$deleteUser$1(UserService.java:55)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.deleteUser(UserService.java:55)
	at com.example.demo.service.UserServiceTest.testDeleteUserNotFound(UserServiceTest.java:167)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 26 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m4">com.example.demo.service.UserServiceTest#testGetAllUsers</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m5">com.example.demo.service.UserServiceTest#testGetUserByEmail</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m6">com.example.demo.service.UserServiceTest#testGetUserById</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m7">com.example.demo.service.UserServiceTest#testGetUserByIdNotFound</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m8">com.example.demo.service.UserServiceTest#testGetUserCount</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m9">com.example.demo.service.UserServiceTest#testUpdateUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m10">com.example.demo.service.UserServiceTest#testUpdateUserNotFound</h3><table class="result"><tr><th>Expected Exception</th></tr><tr><td><div class="stacktrace">java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$updateUser$0(UserService.java:38)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.updateUser(UserService.java:38)
	at com.example.demo.service.UserServiceTest.testUpdateUserNotFound(UserServiceTest.java:144)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 26 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h2>UserControllerTests</h2><h3 id="m11">com.example.demo.controller.UserControllerTest#testCreateUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m12">com.example.demo.controller.UserControllerTest#testCreateUserWithInvalidData</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m13">com.example.demo.controller.UserControllerTest#testDeleteUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m14">com.example.demo.controller.UserControllerTest#testGetAllUsers</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m15">com.example.demo.controller.UserControllerTest#testGetUserByEmail</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m16">com.example.demo.controller.UserControllerTest#testGetUserById</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m17">com.example.demo.controller.UserControllerTest#testGetUserByIdNotFound</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m18">com.example.demo.controller.UserControllerTest#testGetUserCount</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m19">com.example.demo.controller.UserControllerTest#testUpdateUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h2>UserModelTests</h2><h3 id="m20">com.example.demo.model.UserTest#testUserConstructor</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m21">com.example.demo.model.UserTest#testUserToString</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m22">com.example.demo.model.UserTest#testUserWithBlankName</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m23">com.example.demo.model.UserTest#testUserWithInvalidEmail</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m24">com.example.demo.model.UserTest#testUserWithShortPhone</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m25">com.example.demo.model.UserTest#testValidUser</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
</body>
</html>
