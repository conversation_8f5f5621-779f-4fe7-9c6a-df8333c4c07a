<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="9" name="com.example.demo.controller.UserControllerTest" time="1.094" errors="0" timestamp="2025-05-28T00:27:41 IST" skipped="0">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.022"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.032"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.019"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.023"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.040"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.031"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.270"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.062"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.595"/>
</testsuite> <!-- com.example.demo.controller.UserControllerTest -->
