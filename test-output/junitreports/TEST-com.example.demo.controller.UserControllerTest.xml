<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="9" name="com.example.demo.controller.UserControllerTest" time="1.085" errors="0" timestamp="2025-05-28T00:33:02 IST" skipped="0">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.050"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.019"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.536"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.061"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.023"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.291"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.034"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.035"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.036"/>
</testsuite> <!-- com.example.demo.controller.UserControllerTest -->
