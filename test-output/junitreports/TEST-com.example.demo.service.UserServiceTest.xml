<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="11" name="com.example.demo.service.UserServiceTest" time="0.380" errors="0" timestamp="2025-05-28T00:33:02 IST" skipped="0">
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByIdNotFound" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserById" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserCount" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetAllUsers" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUserWithExistingEmail" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUser" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUser" time="0.011"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByEmail" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUserNotFound" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUserNotFound" time="0.366"/>
</testsuite> <!-- com.example.demo.service.UserServiceTest -->
