<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports1.css" rel="stylesheet" id="ultra" />
    <link type="text/css" href="testng-reports.css" rel="stylesheet" id="retro" disabled="false"/>
    <script type="text/javascript" src="jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <button id="button" class="button">Switch Retro Theme</button> <!-- button -->
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" title="Collapse/expand all the suites" class="collapse-all-link">
          <img src="collapseall.gif" class="collapse-all-icon">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" panel-name="suite-SpringBootTestNGSuite" class="navigator-link">
              <span class="suite-name border-passed">SpringBootTestNGSuite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" panel-name="test-xml-SpringBootTestNGSuite" class="navigator-link ">
                    <span>testng.xml</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="testlist-SpringBootTestNGSuite" class="navigator-link ">
                    <span class="test-stats">3 tests</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="group-SpringBootTestNGSuite" class="navigator-link ">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="times-SpringBootTestNGSuite" class="navigator-link ">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="reporter-SpringBootTestNGSuite" class="navigator-link ">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="ignored-methods-SpringBootTestNGSuite" class="navigator-link ">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="chronological-SpringBootTestNGSuite" class="navigator-link ">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">26 methods,   26 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-SpringBootTestNGSuite" class="hide-methods passed suite-SpringBootTestNGSuite"> (hide)</a> <!-- hide-methods passed suite-SpringBootTestNGSuite -->
                      <a href="#" panel-name="suite-SpringBootTestNGSuite" class="show-methods passed suite-SpringBootTestNGSuite"> (show)</a> <!-- show-methods passed suite-SpringBootTestNGSuite -->
                    </span>
                    <div class="method-list-content passed suite-SpringBootTestNGSuite">
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testCreateUser">testCreateUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testCreateUser">testCreateUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testCreateUserWithExistingEmail">testCreateUserWithExistingEmail</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testCreateUserWithInvalidData">testCreateUserWithInvalidData</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testDeleteUser">testDeleteUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testDeleteUser">testDeleteUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testDeleteUserNotFound">testDeleteUserNotFound</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testGetAllUsers">testGetAllUsers</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testGetAllUsers">testGetAllUsers</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testGetUserByEmail">testGetUserByEmail</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testGetUserByEmail">testGetUserByEmail</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testGetUserById">testGetUserById</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testGetUserById">testGetUserById</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testGetUserByIdNotFound">testGetUserByIdNotFound</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testGetUserByIdNotFound">testGetUserByIdNotFound</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testGetUserCount">testGetUserCount</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testGetUserCount">testGetUserCount</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testUpdateUser">testUpdateUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.controller.UserControllerTest" class="method navigator-link" hash-for-method="testUpdateUser">testUpdateUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.service.UserServiceTest" class="method navigator-link" hash-for-method="testUpdateUserNotFound">testUpdateUserNotFound</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testUserConstructor">testUserConstructor</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testUserToString">testUserToString</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testUserWithBlankName">testUserWithBlankName</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testUserWithInvalidEmail">testUserWithInvalidEmail</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testUserWithShortPhone">testUserWithShortPhone</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-SpringBootTestNGSuite" title="com.example.demo.model.UserTest" class="method navigator-link" hash-for-method="testValidUser">testValidUser</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-SpringBootTestNGSuite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-SpringBootTestNGSuite" class="panel SpringBootTestNGSuite">
          <div class="suite-SpringBootTestNGSuite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.example.demo.controller.UserControllerTest</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testCreateUser">
                  </a> <!-- testCreateUser -->
                  <span class="method-name">testCreateUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testCreateUserWithInvalidData">
                  </a> <!-- testCreateUserWithInvalidData -->
                  <span class="method-name">testCreateUserWithInvalidData</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testDeleteUser">
                  </a> <!-- testDeleteUser -->
                  <span class="method-name">testDeleteUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetAllUsers">
                  </a> <!-- testGetAllUsers -->
                  <span class="method-name">testGetAllUsers</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserByEmail">
                  </a> <!-- testGetUserByEmail -->
                  <span class="method-name">testGetUserByEmail</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserById">
                  </a> <!-- testGetUserById -->
                  <span class="method-name">testGetUserById</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserByIdNotFound">
                  </a> <!-- testGetUserByIdNotFound -->
                  <span class="method-name">testGetUserByIdNotFound</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserCount">
                  </a> <!-- testGetUserCount -->
                  <span class="method-name">testGetUserCount</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUpdateUser">
                  </a> <!-- testUpdateUser -->
                  <span class="method-name">testUpdateUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-SpringBootTestNGSuite-class-passed -->
          <div class="suite-SpringBootTestNGSuite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.example.demo.service.UserServiceTest</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testCreateUser">
                  </a> <!-- testCreateUser -->
                  <span class="method-name">testCreateUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testCreateUserWithExistingEmail">
                  </a> <!-- testCreateUserWithExistingEmail -->
                  <span class="method-name">testCreateUserWithExistingEmail</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testDeleteUser">
                  </a> <!-- testDeleteUser -->
                  <span class="method-name">testDeleteUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testDeleteUserNotFound">
                  </a> <!-- testDeleteUserNotFound -->
                  <span class="method-name">testDeleteUserNotFound</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetAllUsers">
                  </a> <!-- testGetAllUsers -->
                  <span class="method-name">testGetAllUsers</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserByEmail">
                  </a> <!-- testGetUserByEmail -->
                  <span class="method-name">testGetUserByEmail</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserById">
                  </a> <!-- testGetUserById -->
                  <span class="method-name">testGetUserById</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserByIdNotFound">
                  </a> <!-- testGetUserByIdNotFound -->
                  <span class="method-name">testGetUserByIdNotFound</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testGetUserCount">
                  </a> <!-- testGetUserCount -->
                  <span class="method-name">testGetUserCount</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUpdateUser">
                  </a> <!-- testUpdateUser -->
                  <span class="method-name">testUpdateUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUpdateUserNotFound">
                  </a> <!-- testUpdateUserNotFound -->
                  <span class="method-name">testUpdateUserNotFound</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-SpringBootTestNGSuite-class-passed -->
          <div class="suite-SpringBootTestNGSuite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.example.demo.model.UserTest</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testUserConstructor">
                  </a> <!-- testUserConstructor -->
                  <span class="method-name">testUserConstructor</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUserToString">
                  </a> <!-- testUserToString -->
                  <span class="method-name">testUserToString</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUserWithBlankName">
                  </a> <!-- testUserWithBlankName -->
                  <span class="method-name">testUserWithBlankName</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUserWithInvalidEmail">
                  </a> <!-- testUserWithInvalidEmail -->
                  <span class="method-name">testUserWithInvalidEmail</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testUserWithShortPhone">
                  </a> <!-- testUserWithShortPhone -->
                  <span class="method-name">testUserWithShortPhone</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testValidUser">
                  </a> <!-- testValidUser -->
                  <span class="method-name">testValidUser</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-SpringBootTestNGSuite-class-passed -->
        </div> <!-- panel SpringBootTestNGSuite -->
        <div panel-name="test-xml-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">/home/<USER>/Documents/augment-projects/java-springboot/src/test/resources/testng.xml</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite name=&quot;SpringBootTestNGSuite&quot; guice-stage=&quot;DEVELOPMENT&quot;&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;UserServiceTests&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.example.demo.service.UserServiceTest&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- UserServiceTests --&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;UserControllerTests&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.example.demo.controller.UserControllerTest&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- UserControllerTests --&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;UserModelTests&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.example.demo.model.UserTest&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- UserModelTests --&gt;
&lt;/suite&gt; &lt;!-- SpringBootTestNGSuite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for SpringBootTestNGSuite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">UserServiceTests (1 class)</span>
              </li>
              <li>
                <span class="test-name">UserControllerTests (1 class)</span>
              </li>
              <li>
                <span class="test-name">UserModelTests (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for SpringBootTestNGSuite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for SpringBootTestNGSuite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_SpringBootTestNGSuite');
function tableData_SpringBootTestNGSuite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(26);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'testCreateUser')
data.setCell(0, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(0, 3, 273);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'testCreateUserWithInvalidData')
data.setCell(1, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(1, 3, 49);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'testGetUserByEmail')
data.setCell(2, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(2, 3, 30);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'testUpdateUser')
data.setCell(3, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(3, 3, 21);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'testGetAllUsers')
data.setCell(4, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(4, 3, 13);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'testCreateUser')
data.setCell(5, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(5, 3, 11);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'testUserWithBlankName')
data.setCell(6, 2, 'com.example.demo.model.UserTest')
data.setCell(6, 3, 11);
data.setCell(7, 0, 7)
data.setCell(7, 1, 'testUserWithInvalidEmail')
data.setCell(7, 2, 'com.example.demo.model.UserTest')
data.setCell(7, 3, 11);
data.setCell(8, 0, 8)
data.setCell(8, 1, 'testDeleteUser')
data.setCell(8, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(8, 3, 8);
data.setCell(9, 0, 9)
data.setCell(9, 1, 'testGetUserCount')
data.setCell(9, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(9, 3, 7);
data.setCell(10, 0, 10)
data.setCell(10, 1, 'testGetUserById')
data.setCell(10, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(10, 3, 6);
data.setCell(11, 0, 11)
data.setCell(11, 1, 'testGetUserByIdNotFound')
data.setCell(11, 2, 'com.example.demo.controller.UserControllerTest')
data.setCell(11, 3, 4);
data.setCell(12, 0, 12)
data.setCell(12, 1, 'testUserWithShortPhone')
data.setCell(12, 2, 'com.example.demo.model.UserTest')
data.setCell(12, 3, 4);
data.setCell(13, 0, 13)
data.setCell(13, 1, 'testValidUser')
data.setCell(13, 2, 'com.example.demo.model.UserTest')
data.setCell(13, 3, 4);
data.setCell(14, 0, 14)
data.setCell(14, 1, 'testDeleteUser')
data.setCell(14, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(14, 3, 1);
data.setCell(15, 0, 15)
data.setCell(15, 1, 'testUpdateUser')
data.setCell(15, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(15, 3, 1);
data.setCell(16, 0, 16)
data.setCell(16, 1, 'testGetUserByIdNotFound')
data.setCell(16, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(16, 3, 0);
data.setCell(17, 0, 17)
data.setCell(17, 1, 'testGetUserById')
data.setCell(17, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(17, 3, 0);
data.setCell(18, 0, 18)
data.setCell(18, 1, 'testGetUserCount')
data.setCell(18, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(18, 3, 0);
data.setCell(19, 0, 19)
data.setCell(19, 1, 'testGetAllUsers')
data.setCell(19, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(19, 3, 0);
data.setCell(20, 0, 20)
data.setCell(20, 1, 'testCreateUserWithExistingEmail')
data.setCell(20, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(20, 3, 0);
data.setCell(21, 0, 21)
data.setCell(21, 1, 'testGetUserByEmail')
data.setCell(21, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(21, 3, 0);
data.setCell(22, 0, 22)
data.setCell(22, 1, 'testDeleteUserNotFound')
data.setCell(22, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(22, 3, 0);
data.setCell(23, 0, 23)
data.setCell(23, 1, 'testUpdateUserNotFound')
data.setCell(23, 2, 'com.example.demo.service.UserServiceTest')
data.setCell(23, 3, 0);
data.setCell(24, 0, 24)
data.setCell(24, 1, 'testUserConstructor')
data.setCell(24, 2, 'com.example.demo.model.UserTest')
data.setCell(24, 3, 0);
data.setCell(25, 0, 25)
data.setCell(25, 1, 'testUserToString')
data.setCell(25, 2, 'com.example.demo.model.UserTest')
data.setCell(25, 3, 0);
window.suiteTableData['SpringBootTestNGSuite']= { tableData: data, tableDiv: 'times-div-SpringBootTestNGSuite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 454 ms</span>
              <div id="times-div-SpringBootTestNGSuite">
              </div> <!-- times-div-SpringBootTestNGSuite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for SpringBootTestNGSuite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-SpringBootTestNGSuite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">com.example.demo.service.UserServiceTest</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testCreateUser</span>
                <span class="method-start">366 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">378 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testCreateUserWithExistingEmail</span>
                <span class="method-start">379 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testDeleteUser</span>
                <span class="method-start">380 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">380 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testDeleteUserNotFound</span>
                <span class="method-start">382 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">382 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetAllUsers</span>
                <span class="method-start">383 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">383 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserByEmail</span>
                <span class="method-start">384 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">384 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserById</span>
                <span class="method-start">385 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">385 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserByIdNotFound</span>
                <span class="method-start">386 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">386 ms</span>
              </div> <!-- configuration-method before -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">387 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserCount</span>
                <span class="method-start">387 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">388 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUpdateUser</span>
                <span class="method-start">389 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">391 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUpdateUserNotFound</span>
                <span class="method-start">391 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.example.demo.controller.UserControllerTest</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">414 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testCreateUser</span>
                <span class="method-start">944 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1220 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testCreateUserWithInvalidData</span>
                <span class="method-start">1241 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1291 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testDeleteUser</span>
                <span class="method-start">1306 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1314 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetAllUsers</span>
                <span class="method-start">1338 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1352 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserByEmail</span>
                <span class="method-start">1379 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1409 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserById</span>
                <span class="method-start">1421 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1428 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserByIdNotFound</span>
                <span class="method-start">1445 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1450 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testGetUserCount</span>
                <span class="method-start">1468 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1476 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUpdateUser</span>
                <span class="method-start">1490 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.example.demo.model.UserTest</div> <!-- chronological-class-name -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1515 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUserConstructor</span>
                <span class="method-start">1520 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1521 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUserToString</span>
                <span class="method-start">1526 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1527 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUserWithBlankName</span>
                <span class="method-start">1533 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1545 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUserWithInvalidEmail</span>
                <span class="method-start">1551 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1563 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testUserWithShortPhone</span>
                <span class="method-start">1569 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">setUp</span>
                <span class="method-start">1573 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testValidUser</span>
                <span class="method-start">1577 ms</span>
              </div> <!-- test-method -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
<script type="text/javascript" src="testng-reports2.js"></script>
</html>
