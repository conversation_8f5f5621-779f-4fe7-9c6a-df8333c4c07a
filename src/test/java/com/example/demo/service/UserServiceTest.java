package com.example.demo.service;

import com.example.demo.model.User;
import com.example.demo.repository.UserRepository;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

public class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
    
    private User testUser;
    
    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        testUser = new User("John Doe", "<EMAIL>", "1234567890");
        testUser.setId(1L);
    }
    
    @Test
    public void testGetAllUsers() {
        // Arrange
        List<User> users = Arrays.asList(testUser, new User("<PERSON>", "<EMAIL>", "9876543210"));
        when(userRepository.findAll()).thenReturn(users);
        
        // Act
        List<User> result = userService.getAllUsers();
        
        // Assert
        assertEquals(2, result.size());
        verify(userRepository, times(1)).findAll();
    }
    
    @Test
    public void testGetUserById() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        
        // Act
        Optional<User> result = userService.getUserById(1L);
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals("John Doe", result.get().getName());
        verify(userRepository, times(1)).findById(1L);
    }
    
    @Test
    public void testGetUserByIdNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());
        
        // Act
        Optional<User> result = userService.getUserById(999L);
        
        // Assert
        assertFalse(result.isPresent());
        verify(userRepository, times(1)).findById(999L);
    }
    
    @Test
    public void testGetUserByEmail() {
        // Arrange
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
        
        // Act
        Optional<User> result = userService.getUserByEmail("<EMAIL>");
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals("John Doe", result.get().getName());
        verify(userRepository, times(1)).findByEmail("<EMAIL>");
    }
    
    @Test
    public void testCreateUser() {
        // Arrange
        User newUser = new User("Jane Doe", "<EMAIL>", "9876543210");
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(newUser);
        
        // Act
        User result = userService.createUser(newUser);
        
        // Assert
        assertEquals("Jane Doe", result.getName());
        verify(userRepository, times(1)).existsByEmail("<EMAIL>");
        verify(userRepository, times(1)).save(newUser);
    }
    
    @Test(expectedExceptions = RuntimeException.class, 
          expectedExceptionsMessageRegExp = "User with email .* already exists")
    public void testCreateUserWithExistingEmail() {
        // Arrange
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);
        
        // Act
        userService.createUser(testUser);
    }
    
    @Test
    public void testUpdateUser() {
        // Arrange
        User updatedDetails = new User("John Smith", "<EMAIL>", "5555555555");
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        
        // Act
        User result = userService.updateUser(1L, updatedDetails);
        
        // Assert
        assertEquals("John Smith", result.getName());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals("5555555555", result.getPhone());
        verify(userRepository, times(1)).findById(1L);
        verify(userRepository, times(1)).save(testUser);
    }
    
    @Test(expectedExceptions = RuntimeException.class,
          expectedExceptionsMessageRegExp = "User not found with id: .*")
    public void testUpdateUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());
        
        // Act
        userService.updateUser(999L, testUser);
    }
    
    @Test
    public void testDeleteUser() {
        // Arrange
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        
        // Act
        userService.deleteUser(1L);
        
        // Assert
        verify(userRepository, times(1)).findById(1L);
        verify(userRepository, times(1)).delete(testUser);
    }
    
    @Test(expectedExceptions = RuntimeException.class,
          expectedExceptionsMessageRegExp = "User not found with id: .*")
    public void testDeleteUserNotFound() {
        // Arrange
        when(userRepository.findById(999L)).thenReturn(Optional.empty());
        
        // Act
        userService.deleteUser(999L);
    }
    
    @Test
    public void testGetUserCount() {
        // Arrange
        when(userRepository.count()).thenReturn(5L);
        
        // Act
        long result = userService.getUserCount();
        
        // Assert
        assertEquals(5L, result);
        verify(userRepository, times(1)).count();
    }
}
