package com.example.demo.model;

import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.testng.Assert.*;

public class UserTest {
    
    private Validator validator;
    private User user;
    
    @BeforeMethod
    public void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
        user = new User();
    }
    
    @Test
    public void testValidUser() {
        user.setName("John Doe");
        user.setEmail("<EMAIL>");
        user.setPhone("1234567890");
        
        Set<ConstraintViolation<User>> violations = validator.validate(user);
        assertTrue(violations.isEmpty(), "Valid user should have no validation errors");
    }
    
    @Test
    public void testUserWithBlankName() {
        user.setName("");
        user.setEmail("<EMAIL>");
        user.setPhone("1234567890");
        
        Set<ConstraintViolation<User>> violations = validator.validate(user);
        assertFalse(violations.isEmpty(), "User with blank name should have validation errors");
        
        boolean hasNameError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("name"));
        assertTrue(hasNameError, "Should have validation error for name field");
    }
    
    @Test
    public void testUserWithInvalidEmail() {
        user.setName("John Doe");
        user.setEmail("invalid-email");
        user.setPhone("1234567890");
        
        Set<ConstraintViolation<User>> violations = validator.validate(user);
        assertFalse(violations.isEmpty(), "User with invalid email should have validation errors");
        
        boolean hasEmailError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("email"));
        assertTrue(hasEmailError, "Should have validation error for email field");
    }
    
    @Test
    public void testUserWithShortPhone() {
        user.setName("John Doe");
        user.setEmail("<EMAIL>");
        user.setPhone("123");
        
        Set<ConstraintViolation<User>> violations = validator.validate(user);
        assertFalse(violations.isEmpty(), "User with short phone should have validation errors");
        
        boolean hasPhoneError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("phone"));
        assertTrue(hasPhoneError, "Should have validation error for phone field");
    }
    
    @Test
    public void testUserConstructor() {
        User user = new User("Jane Doe", "<EMAIL>", "9876543210");
        
        assertEquals("Jane Doe", user.getName());
        assertEquals("<EMAIL>", user.getEmail());
        assertEquals("9876543210", user.getPhone());
    }
    
    @Test
    public void testUserToString() {
        user.setId(1L);
        user.setName("John Doe");
        user.setEmail("<EMAIL>");
        user.setPhone("1234567890");
        
        String expected = "User{id=1, name='John Doe', email='<EMAIL>', phone='1234567890'}";
        assertEquals(expected, user.toString());
    }
}
