import org.testng.TestNG;
import org.testng.xml.XmlSuite;
import org.testng.xml.Parser;

import java.io.FileInputStream;
import java.util.List;

public class TestLister {
    public static void main(String[] args) {
        try {
            // Parse the TestNG XML file
            Parser parser = new Parser("src/test/resources/testng.xml");
            List<XmlSuite> suites = parser.parseToList();
            
            System.out.println("=== TestNG Test Cases ===");
            
            for (XmlSuite suite : suites) {
                System.out.println("Suite: " + suite.getName());
                
                suite.getTests().forEach(test -> {
                    System.out.println("  Test: " + test.getName());
                    
                    test.getClasses().forEach(xmlClass -> {
                        System.out.println("    Class: " + xmlClass.getName());
                        
                        try {
                            Class<?> clazz = Class.forName(xmlClass.getName());
                            java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();
                            
                            for (java.lang.reflect.Method method : methods) {
                                if (method.isAnnotationPresent(org.testng.annotations.Test.class)) {
                                    System.out.println("      - " + method.getName());
                                }
                            }
                        } catch (ClassNotFoundException e) {
                            System.out.println("      Error loading class: " + e.getMessage());
                        }
                    });
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
