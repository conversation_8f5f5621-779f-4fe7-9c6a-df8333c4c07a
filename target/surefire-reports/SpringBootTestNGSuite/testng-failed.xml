<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Failed suite [SpringBootTestNGSuite]" guice-stage="DEVELOPMENT" verbose="0">
  <test thread-count="5" name="UserServiceTests(failed)" verbose="0">
    <classes>
      <class name="com.example.demo.service.UserServiceTest">
        <methods>
          <include name="testCreateUserWithExistingEmail"/>
          <include name="testUpdateUserNotFound"/>
          <include name="testDeleteUserNotFound"/>
          <include name="setUp"/>
        </methods>
      </class> <!-- com.example.demo.service.UserServiceTest -->
    </classes>
  </test> <!-- UserServiceTests(failed) -->
</suite> <!-- Failed suite [SpringBootTestNGSuite] -->
