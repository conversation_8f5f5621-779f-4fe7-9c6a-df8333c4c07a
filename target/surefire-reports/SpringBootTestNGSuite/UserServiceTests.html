<html>
<head>
<title>TestNG:  UserServiceTests</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>UserServiceTests</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>11/0/0</td>
</tr><tr>
<td>Started on:</td><td>Wed May 28 00:21:46 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>0 seconds (476 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td><b>Attribute(s)</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testDeleteUserNotFound()'><b>testDeleteUserNotFound</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td><div><pre>java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$deleteUser$1(UserService.java:55)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.deleteUser(UserService.java:55)
	at com.example.demo.service.UserServiceTest.testDeleteUserNotFound(UserServiceTest.java:167)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 31 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1911168986", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1911168986'><pre>java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$deleteUser$1(UserService.java:55)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.deleteUser(UserService.java:55)
	at com.example.demo.service.UserServiceTest.testDeleteUserNotFound(UserServiceTest.java:167)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:819)
	at org.testng.TestRunner.run(TestRunner.java:619)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
	at org.testng.TestNG.runSuites(TestNG.java:1134)
	at org.testng.TestNG.run(TestNG.java:1101)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
</pre></div></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testGetUserByEmail()'><b>testGetUserByEmail</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testGetUserById()'><b>testGetUserById</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testUpdateUserNotFound()'><b>testUpdateUserNotFound</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td><div><pre>java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$updateUser$0(UserService.java:38)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.updateUser(UserService.java:38)
	at com.example.demo.service.UserServiceTest.testUpdateUserNotFound(UserServiceTest.java:144)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 31 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace514453513", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace514453513'><pre>java.lang.RuntimeException: User not found with id: 999
	at com.example.demo.service.UserService.lambda$updateUser$0(UserService.java:38)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.example.demo.service.UserService.updateUser(UserService.java:38)
	at com.example.demo.service.UserServiceTest.testUpdateUserNotFound(UserServiceTest.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:819)
	at org.testng.TestRunner.run(TestRunner.java:619)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
	at org.testng.TestNG.runSuites(TestNG.java:1134)
	at org.testng.TestNG.run(TestNG.java:1101)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
</pre></div></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testDeleteUser()'><b>testDeleteUser</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testUpdateUser()'><b>testUpdateUser</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testGetUserCount()'><b>testGetUserCount</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testCreateUserWithExistingEmail()'><b>testCreateUserWithExistingEmail</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td><div><pre>java.lang.RuntimeException: User <NAME_EMAIL> already exists
	at com.example.demo.service.UserService.createUser(UserService.java:31)
	at com.example.demo.service.UserServiceTest.testCreateUserWithExistingEmail(UserServiceTest.java:115)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 31 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace571956559", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace571956559'><pre>java.lang.RuntimeException: User <NAME_EMAIL> already exists
	at com.example.demo.service.UserService.createUser(UserService.java:31)
	at com.example.demo.service.UserServiceTest.testCreateUserWithExistingEmail(UserServiceTest.java:115)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:819)
	at org.testng.TestRunner.run(TestRunner.java:619)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
	at org.testng.TestNG.runSuites(TestNG.java:1134)
	at org.testng.TestNG.run(TestNG.java:1101)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
</pre></div></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testGetAllUsers()'><b>testGetAllUsers</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testCreateUser()'><b>testCreateUser</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
<tr>
<td title='com.example.demo.service.UserServiceTest.testGetUserByIdNotFound()'><b>testGetUserByIdNotFound</b><br>Test class: com.example.demo.service.UserServiceTest</td>
<td></td>
<td>0</td>
<td>com.example.demo.service.UserServiceTest@4567f35d</td></tr>
</table><p>
</body>
</html>