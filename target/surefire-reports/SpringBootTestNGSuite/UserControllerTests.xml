<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="mathilavdell" failures="0" tests="9" name="UserControllerTests" time="1.111" errors="0" timestamp="2025-05-28T00:21:48 IST">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.236"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.041"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.013"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.017"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.043"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.011"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.005"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.005"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.022"/>
</testsuite> <!-- UserControllerTests -->
