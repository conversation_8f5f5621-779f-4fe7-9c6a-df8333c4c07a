<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="mathilavdell" failures="0" tests="9" name="UserControllerTests" time="1.025" errors="0" timestamp="2025-05-28T00:24:52 IST">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.236"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.049"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.011"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.012"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.026"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.01"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.007"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.007"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.02"/>
</testsuite> <!-- UserControllerTests -->
