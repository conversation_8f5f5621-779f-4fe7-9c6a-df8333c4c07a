<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="mathilavdell" failures="3" tests="11" name="UserServiceTests" time="0.06" errors="0" timestamp="2025-05-28T00:28:22 IST">
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUserWithExistingEmail" time="0.0">
    <failure type="org.testng.TestException" message="
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- testCreateUserWithExistingEmail -->
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUser" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUserNotFound" time="0.0">
    <failure type="org.testng.TestException" message="
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- testDeleteUserNotFound -->
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetAllUsers" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByEmail" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserById" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByIdNotFound" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserCount" time="0.0"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUser" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUserNotFound" time="0.001">
    <failure type="org.testng.TestException" message="
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- testUpdateUserNotFound -->
</testsuite> <!-- UserServiceTests -->
