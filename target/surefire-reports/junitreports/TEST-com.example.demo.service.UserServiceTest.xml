<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="11" name="com.example.demo.service.UserServiceTest" time="0.016" errors="3" timestamp="2025-05-28T00:28:22 IST" skipped="0">
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserCount" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetAllUsers" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByEmail" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserById" time="0.000"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUserNotFound" time="0.007">
    <error message="
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException" type="org.testng.TestException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
    </error>
  </testcase> <!-- testDeleteUserNotFound -->
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUserWithExistingEmail" time="0.000">
    <error message="
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException" type="org.testng.TestException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
    </error>
  </testcase> <!-- testCreateUserWithExistingEmail -->
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUserNotFound" time="0.001">
    <error message="
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException" type="org.testng.TestException">
      <![CDATA[org.testng.TestException: 
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
    </error>
  </testcase> <!-- testUpdateUserNotFound -->
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByIdNotFound" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUser" time="0.001"/>
</testsuite> <!-- com.example.demo.service.UserServiceTest -->
