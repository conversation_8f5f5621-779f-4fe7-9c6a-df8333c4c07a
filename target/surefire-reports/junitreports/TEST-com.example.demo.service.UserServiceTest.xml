<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="11" name="com.example.demo.service.UserServiceTest" time="0.436" errors="0" timestamp="2025-05-28T00:21:48 IST" skipped="0">
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUserNotFound" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByEmail" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserById" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUserNotFound" time="0.407"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testDeleteUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testUpdateUser" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserCount" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUserWithExistingEmail" time="0.001"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetAllUsers" time="0.002"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testCreateUser" time="0.016"/>
  <testcase classname="com.example.demo.service.UserServiceTest" name="testGetUserByIdNotFound" time="0.002"/>
</testsuite> <!-- com.example.demo.service.UserServiceTest -->
