<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="9" name="com.example.demo.controller.UserControllerTest" time="1.017" errors="0" timestamp="2025-05-28T00:24:52 IST" skipped="0">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.024"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.021"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.518"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.023"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.035"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.044"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.067"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.037"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.248"/>
</testsuite> <!-- com.example.demo.controller.UserControllerTest -->
