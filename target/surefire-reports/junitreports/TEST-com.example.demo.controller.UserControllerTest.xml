<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="mathilavdell" failures="0" tests="9" name="com.example.demo.controller.UserControllerTest" time="1.100" errors="0" timestamp="2025-05-28T00:21:48 IST" skipped="0">
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserById" time="0.026"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserCount" time="0.025"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByIdNotFound" time="0.579"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testDeleteUser" time="0.026"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetAllUsers" time="0.039"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testGetUserByEmail" time="0.063"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUserWithInvalidData" time="0.058"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testUpdateUser" time="0.037"/>
  <testcase classname="com.example.demo.controller.UserControllerTest" name="testCreateUser" time="0.247"/>
</testsuite> <!-- com.example.demo.controller.UserControllerTest -->
