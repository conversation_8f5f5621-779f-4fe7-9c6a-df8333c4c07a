<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="0" total="26" passed="26" failed="0" skipped="0">
  <reporter-output>
  </reporter-output>
  <suite started-at="2025-05-28T00:21:46 IST" name="SpringBootTestNGSuite" finished-at="2025-05-28T00:21:48 IST" duration-ms="1690">
    <groups>
    </groups>
    <test started-at="2025-05-28T00:21:46 IST" name="UserServiceTests" finished-at="2025-05-28T00:21:47 IST" duration-ms="476">
      <class name="com.example.demo.service.UserServiceTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:46 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="406" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testCreateUser" finished-at="2025-05-28T00:21:47 IST" duration-ms="16" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testCreateUserWithExistingEmail" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <exception class="java.lang.RuntimeException">
            <message>
              <![CDATA[User <NAME_EMAIL> already exists]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.RuntimeException: User <NAME_EMAIL> already exists
at com.example.demo.service.UserService.createUser(UserService.java:31)
at com.example.demo.service.UserServiceTest.testCreateUserWithExistingEmail(UserServiceTest.java:115)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:569)
at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.RuntimeException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUserWithExistingEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testDeleteUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testDeleteUser" finished-at="2025-05-28T00:21:47 IST" duration-ms="2" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testDeleteUserNotFound" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <exception class="java.lang.RuntimeException">
            <message>
              <![CDATA[User not found with id: 999]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.RuntimeException: User not found with id: 999
at com.example.demo.service.UserService.lambda$deleteUser$1(UserService.java:55)
at java.base/java.util.Optional.orElseThrow(Optional.java:403)
at com.example.demo.service.UserService.deleteUser(UserService.java:55)
at com.example.demo.service.UserServiceTest.testDeleteUserNotFound(UserServiceTest.java:167)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:569)
at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.RuntimeException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUserNotFound -->
        <test-method signature="testGetAllUsers()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testGetAllUsers" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetAllUsers -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserByEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testGetUserByEmail" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserById()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testGetUserById" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserById -->
        <test-method signature="testGetUserByIdNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testGetUserByIdNotFound" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByIdNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserCount()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testGetUserCount" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserCount -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testUpdateUser" finished-at="2025-05-28T00:21:47 IST" duration-ms="2" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="testUpdateUserNotFound" finished-at="2025-05-28T00:21:47 IST" duration-ms="1" status="PASS">
          <exception class="java.lang.RuntimeException">
            <message>
              <![CDATA[User not found with id: 999]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.RuntimeException: User not found with id: 999
at com.example.demo.service.UserService.lambda$updateUser$0(UserService.java:38)
at java.base/java.util.Optional.orElseThrow(Optional.java:403)
at com.example.demo.service.UserService.updateUser(UserService.java:38)
at com.example.demo.service.UserServiceTest.testUpdateUserNotFound(UserServiceTest.java:144)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.base/java.lang.reflect.Method.invoke(Method.java:569)
at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.RuntimeException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUserNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
      </class> <!-- com.example.demo.service.UserServiceTest -->
    </test> <!-- UserServiceTests -->
    <test started-at="2025-05-28T00:21:47 IST" name="UserControllerTests" finished-at="2025-05-28T00:21:48 IST" duration-ms="1111">
      <class name="com.example.demo.controller.UserControllerTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:47 IST" duration-ms="574" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:47 IST" name="testCreateUser" finished-at="2025-05-28T00:21:47 IST" duration-ms="236" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:47 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="17" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUserWithInvalidData()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testCreateUserWithInvalidData" finished-at="2025-05-28T00:21:48 IST" duration-ms="41" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUserWithInvalidData -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="20" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testDeleteUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testDeleteUser" finished-at="2025-05-28T00:21:48 IST" duration-ms="13" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="15" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetAllUsers()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testGetAllUsers" finished-at="2025-05-28T00:21:48 IST" duration-ms="17" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetAllUsers -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="15" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserByEmail()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testGetUserByEmail" finished-at="2025-05-28T00:21:48 IST" duration-ms="43" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="11" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserById()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testGetUserById" finished-at="2025-05-28T00:21:48 IST" duration-ms="11" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserById -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="20" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserByIdNotFound()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testGetUserByIdNotFound" finished-at="2025-05-28T00:21:48 IST" duration-ms="5" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByIdNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="22" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserCount()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testGetUserCount" finished-at="2025-05-28T00:21:48 IST" duration-ms="5" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserCount -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="13" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:21:48 IST" name="testUpdateUser" finished-at="2025-05-28T00:21:48 IST" duration-ms="22" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUser -->
      </class> <!-- com.example.demo.controller.UserControllerTest -->
    </test> <!-- UserControllerTests -->
    <test started-at="2025-05-28T00:21:48 IST" name="UserModelTests" finished-at="2025-05-28T00:21:48 IST" duration-ms="69">
      <class name="com.example.demo.model.UserTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="5" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserConstructor()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testUserConstructor" finished-at="2025-05-28T00:21:48 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserConstructor -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="4" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserToString()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testUserToString" finished-at="2025-05-28T00:21:48 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserToString -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="5" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithBlankName()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testUserWithBlankName" finished-at="2025-05-28T00:21:48 IST" duration-ms="10" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithBlankName -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="4" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithInvalidEmail()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testUserWithInvalidEmail" finished-at="2025-05-28T00:21:48 IST" duration-ms="9" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithInvalidEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="6" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithShortPhone()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testUserWithShortPhone" finished-at="2025-05-28T00:21:48 IST" duration-ms="8" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithShortPhone -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="setUp" finished-at="2025-05-28T00:21:48 IST" duration-ms="3" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testValidUser()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:21:48 IST" name="testValidUser" finished-at="2025-05-28T00:21:48 IST" duration-ms="4" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testValidUser -->
      </class> <!-- com.example.demo.model.UserTest -->
    </test> <!-- UserModelTests -->
  </suite> <!-- SpringBootTestNGSuite -->
</testng-results>
