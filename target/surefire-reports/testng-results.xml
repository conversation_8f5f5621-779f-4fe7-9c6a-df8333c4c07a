<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="0" total="26" passed="23" failed="3" skipped="0">
  <reporter-output>
  </reporter-output>
  <suite started-at="2025-05-28T00:28:22 IST" name="SpringBootTestNGSuite" finished-at="2025-05-28T00:28:22 IST" duration-ms="124">
    <groups>
    </groups>
    <test started-at="2025-05-28T00:28:22 IST" name="UserServiceTests" finished-at="2025-05-28T00:28:22 IST" duration-ms="60">
      <class name="com.example.demo.service.UserServiceTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="7" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testCreateUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="2" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testCreateUserWithExistingEmail" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="FAIL">
          <exception class="org.testng.TestException">
            <message>
              <![CDATA[
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException]]>
            </message>
            <full-stacktrace>
              <![CDATA[org.testng.TestException: 
Method UserServiceTest.testCreateUserWithExistingEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- org.testng.TestException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUserWithExistingEmail -->
        <test-method signature="testDeleteUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testDeleteUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testDeleteUserNotFound" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="FAIL">
          <exception class="org.testng.TestException">
            <message>
              <![CDATA[
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException]]>
            </message>
            <full-stacktrace>
              <![CDATA[org.testng.TestException: 
Method UserServiceTest.testDeleteUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- org.testng.TestException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUserNotFound -->
        <test-method signature="testGetAllUsers()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testGetAllUsers" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetAllUsers -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserByEmail()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testGetUserByEmail" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserById()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testGetUserById" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserById -->
        <test-method signature="testGetUserByIdNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testGetUserByIdNotFound" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByIdNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserCount()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testGetUserCount" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserCount -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUser()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testUpdateUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="testUpdateUserNotFound" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="FAIL">
          <exception class="org.testng.TestException">
            <message>
              <![CDATA[
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException]]>
            </message>
            <full-stacktrace>
              <![CDATA[org.testng.TestException: 
Method UserServiceTest.testUpdateUserNotFound()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d] should have thrown an exception of type class java.lang.RuntimeException
at org.testng.internal.invokers.ExpectedExceptionsHolder.noException(ExpectedExceptionsHolder.java:81)
at org.testng.internal.invokers.TestInvoker.considerExceptions(TestInvoker.java:863)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:718)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:228)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:63)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:961)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:201)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:819)
at org.testng.TestRunner.run(TestRunner.java:619)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1301)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1228)
at org.testng.TestNG.runSuites(TestNG.java:1134)
at org.testng.TestNG.run(TestNG.java:1101)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:324)
at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:74)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:123)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]>
            </full-stacktrace>
          </exception> <!-- org.testng.TestException -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUserNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.service.UserServiceTest@4567f35d]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
      </class> <!-- com.example.demo.service.UserServiceTest -->
    </test> <!-- UserServiceTests -->
    <test started-at="2025-05-28T00:28:22 IST" name="UserControllerTests" finished-at="2025-05-28T00:28:22 IST" duration-ms="13">
      <class name="com.example.demo.controller.UserControllerTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testCreateUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testCreateUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUser -->
        <test-method signature="testCreateUserWithInvalidData()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testCreateUserWithInvalidData" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCreateUserWithInvalidData -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testDeleteUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testDeleteUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDeleteUser -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetAllUsers()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testGetAllUsers" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetAllUsers -->
        <test-method signature="testGetUserByEmail()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testGetUserByEmail" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserById()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testGetUserById" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserById -->
        <test-method signature="testGetUserByIdNotFound()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testGetUserByIdNotFound" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserByIdNotFound -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testGetUserCount()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testGetUserCount" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testGetUserCount -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUpdateUser()[pri:0, instance:com.example.demo.controller.UserControllerTest@3023df74]" started-at="2025-05-28T00:28:22 IST" name="testUpdateUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUpdateUser -->
      </class> <!-- com.example.demo.controller.UserControllerTest -->
    </test> <!-- UserControllerTests -->
    <test started-at="2025-05-28T00:28:22 IST" name="UserModelTests" finished-at="2025-05-28T00:28:22 IST" duration-ms="8">
      <class name="com.example.demo.model.UserTest">
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserConstructor()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testUserConstructor" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserConstructor -->
        <test-method signature="testUserToString()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testUserToString" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserToString -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithBlankName()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testUserWithBlankName" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithBlankName -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithInvalidEmail()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testUserWithInvalidEmail" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithInvalidEmail -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testUserWithShortPhone()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testUserWithShortPhone" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testUserWithShortPhone -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method is-config="true" signature="setUp()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="setUp" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setUp -->
        <test-method signature="testValidUser()[pri:0, instance:com.example.demo.model.UserTest@7c7b252e]" started-at="2025-05-28T00:28:22 IST" name="testValidUser" finished-at="2025-05-28T00:28:22 IST" duration-ms="0" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testValidUser -->
      </class> <!-- com.example.demo.model.UserTest -->
    </test> <!-- UserModelTests -->
  </suite> <!-- SpringBootTestNGSuite -->
</testng-results>
