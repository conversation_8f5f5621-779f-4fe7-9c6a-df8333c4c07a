# Spring Boot Demo Project with TestNG

A comprehensive demo Spring Boot application showcasing REST API development with TestNG test cases and Maven build system.

## 🚀 Project Overview

This project demonstrates a complete Spring Boot application with:
- RESTful API endpoints for User management
- Comprehensive TestNG test suite
- Maven build configuration
- H2 in-memory database
- Bean validation
- Proper layered architecture

## 📋 Prerequisites

- **Java 17** or higher
- **Maven 3.6+**
- **Git** (optional)

## 🛠️ Technology Stack

- **Spring Boot**: 2.7.18
- **Java**: 17
- **TestNG**: 7.9.0
- **Maven**: Build tool
- **H2 Database**: In-memory database
- **Spring Data JPA**: Data persistence
- **Hibernate Validator**: Bean validation
- **Mockito**: Mocking framework for tests

## 📁 Project Structure

```
src/
├── main/
│   ├── java/com/example/demo/
│   │   ├── DemoApplication.java          # Main application class
│   │   ├── controller/
│   │   │   └── UserController.java       # REST controller
│   │   ├── service/
│   │   │   └── UserService.java          # Business logic layer
│   │   ├── repository/
│   │   │   └── UserRepository.java       # Data access layer
│   │   └── model/
│   │       └── User.java                 # Entity model
│   └── resources/
│       └── application.properties        # Configuration
└── test/
    ├── java/com/example/demo/
    │   ├── controller/
    │   │   └── UserControllerTest.java    # Controller tests
    │   ├── service/
    │   │   └── UserServiceTest.java       # Service tests
    │   └── model/
    │       └── UserTest.java              # Model validation tests
    └── resources/
        └── testng.xml                     # TestNG configuration
```

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd java-springboot
```

### 2. Build the Project
```bash
mvn clean compile
```

### 3. Run Tests
```bash
mvn test
```

### 4. Run the Application
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

## 📚 API Endpoints

### User Management API

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/users` | Get all users |
| GET | `/api/users/{id}` | Get user by ID |
| GET | `/api/users/email/{email}` | Get user by email |
| POST | `/api/users` | Create new user |
| PUT | `/api/users/{id}` | Update user |
| DELETE | `/api/users/{id}` | Delete user |
| GET | `/api/users/count` | Get user count |

### Sample API Calls

#### Get All Users
```bash
curl -X GET http://localhost:8080/api/users
```

#### Create a New User
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890"
  }'
```

#### Get User by ID
```bash
curl -X GET http://localhost:8080/api/users/1
```

## 🧪 Testing

The project includes comprehensive TestNG test suites:

### Test Categories

1. **Model Tests** (`UserTest.java`)
   - Bean validation testing
   - Constructor and method testing
   - toString() method validation

2. **Service Tests** (`UserServiceTest.java`)
   - Business logic testing
   - Mock repository interactions
   - Exception handling

3. **Controller Tests** (`UserControllerTest.java`)
   - REST endpoint testing
   - HTTP status code validation
   - Request/response testing

### Running Specific Test Suites

```bash
# Run all tests
mvn test

# Run tests with verbose output
mvn test -Dtest.verbose=true

# Run specific test class
mvn test -Dtest=UserServiceTest
```

## 🗄️ Database

The application uses H2 in-memory database for development and testing:

- **Database URL**: `jdbc:h2:mem:testdb`
- **Username**: `sa`
- **Password**: `password`
- **H2 Console**: `http://localhost:8080/h2-console` (when running)

### Sample Data

The application automatically creates sample users on startup:
- Alice Johnson (<EMAIL>)
- Bob Smith (<EMAIL>)
- Charlie Brown (<EMAIL>)

## 🔧 Configuration

Key configuration properties in `application.properties`:

```properties
# Server Configuration
server.port=8080

# Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# H2 Console
spring.h2.console.enabled=true
```

## 📝 User Model Validation

The User entity includes validation constraints:

- **Name**: Required, 2-50 characters
- **Email**: Required, valid email format, unique
- **Phone**: Optional, 10-15 characters

## 🏗️ Build and Package

### Create JAR file
```bash
mvn clean package
```

### Run the JAR
```bash
java -jar target/demo-springboot-testng-1.0.0.jar
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **Port 8080 already in use**
   - Change the port in `application.properties`: `server.port=8081`

2. **Tests failing**
   - Ensure Java 17 is being used
   - Run `mvn clean` before `mvn test`

3. **Database connection issues**
   - Check H2 console at `http://localhost:8080/h2-console`
   - Verify database URL: `jdbc:h2:mem:testdb`

## 📞 Support

For questions or issues, please create an issue in the repository or contact the development team.
